import axios from "axios";

export class NotificationServiceClient {
  private baseURL: string;

  constructor() {
    // Use environment variable or default to localhost for development
    this.baseURL = process.env.NOTIFICATION_SERVICE_URL || "http://localhost:9082";
  }

  /**
   * Notify clients when a new product is added to a brand they have favorited
   */
  async notifyNewProductInFavoriteBrand(
    productId: string,
    brandId: string,
    productName: string,
    brandName: string
  ): Promise<{ success: boolean; clientsNotified: number }> {
    try {
      const response = await axios.post(
        `${this.baseURL}/api/notifications/new-product-in-favorite-brand`,
        {
          productId,
          brandId,
          productName,
          brandName,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 10000, // 10 second timeout
        }
      );

      return {
        success: response.data.success,
        clientsNotified: response.data.clientsNotified || 0,
      };
    } catch (error) {
      console.error("Error calling notification service for new product:", error);
      // Return success false but don't throw to avoid breaking the main flow
      return {
        success: false,
        clientsNotified: 0,
      };
    }
  }

  /**
   * Notify clients when a product they have favorited is updated
   */
  async notifyProductUpdate(
    productId: string,
    productName: string,
    brandName: string,
    updateType: "price" | "details" | "general",
    oldPrice?: number,
    newPrice?: number
  ): Promise<{ success: boolean; clientsNotified: number }> {
    try {
      const response = await axios.post(
        `${this.baseURL}/api/notifications/product-update`,
        {
          productId,
          productName,
          brandName,
          updateType,
          oldPrice,
          newPrice,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 10000, // 10 second timeout
        }
      );

      return {
        success: response.data.success,
        clientsNotified: response.data.clientsNotified || 0,
      };
    } catch (error) {
      console.error("Error calling notification service for product update:", error);
      // Return success false but don't throw to avoid breaking the main flow
      return {
        success: false,
        clientsNotified: 0,
      };
    }
  }

  /**
   * Get count of clients with specific brand in favorites
   */
  async getFavoriteBrandCount(brandId: string): Promise<number> {
    try {
      const response = await axios.get(
        `${this.baseURL}/api/notifications/favorite-brand-count/${brandId}`,
        {
          timeout: 5000, // 5 second timeout
        }
      );

      return response.data.clientsCount || 0;
    } catch (error) {
      console.error("Error getting favorite brand count:", error);
      return 0;
    }
  }

  /**
   * Get count of clients with specific product in favorites
   */
  async getFavoriteProductCount(productId: string): Promise<number> {
    try {
      const response = await axios.get(
        `${this.baseURL}/api/notifications/favorite-product-count/${productId}`,
        {
          timeout: 5000, // 5 second timeout
        }
      );

      return response.data.clientsCount || 0;
    } catch (error) {
      console.error("Error getting favorite product count:", error);
      return 0;
    }
  }
}

// Export a singleton instance
export const notificationServiceClient = new NotificationServiceClient();
